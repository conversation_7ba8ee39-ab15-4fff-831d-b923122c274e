{"summary": {"total_tests": 888, "passed": 506, "failed": 0, "skipped": 382, "success_rate": 56.981981981981974}, "results": [{"function": "Role.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.438153"}, {"function": "Role.get_all_permissions", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'set'>", "error": null, "timestamp": "2025-08-02T08:30:52.451038"}, {"function": "CustomUserManager.create_user", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.473182"}, {"function": "CustomUserManager.create_superuser", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.477403"}, {"function": "CustomUser.get_full_name", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.483424"}, {"function": "CustomUser.get_short_name", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.486236"}, {"function": "CustomUser.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.489047"}, {"function": "CustomUser.get_profile", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'accounts.models.CustomUserProfile'>", "error": null, "timestamp": "2025-08-02T08:30:52.497703"}, {"function": "CustomUserProfile.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.500076"}, {"function": "CustomUserProfile.get_role", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.502400"}, {"function": "AuditLog.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.510154"}, {"function": "EncryptedField.__init__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.534072"}, {"function": "EncryptedField.get_internal_type", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.551336"}, {"function": "EncryptedField.from_db_value", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.574988"}, {"function": "EncryptedField.to_python", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.577259"}, {"function": "EncryptedField.get_prep_value", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.581942"}, {"function": "EncryptedField.encrypt", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.583838"}, {"function": "EncryptedField.decrypt", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.585165"}, {"function": "EncryptedTextField.get_internal_type", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.586069"}, {"function": "EncryptedTextField.__init__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.587806"}, {"function": "Department.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.589565"}, {"function": "create_user", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.590990"}, {"function": "create_superuser", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.593175"}, {"function": "Patient.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.599560"}, {"function": "Patient.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.600554"}, {"function": "Patient.get_full_name", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.602336"}, {"function": "Patient.get_age", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'int'>", "error": null, "timestamp": "2025-08-02T08:30:52.603368"}, {"function": "Patient.age", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Property accessed successfully", "error": null, "timestamp": "2025-08-02T08:30:52.604467"}, {"function": "Patient.get_profile_image", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'NoneType'>", "error": null, "timestamp": "2025-08-02T08:30:52.607268"}, {"function": "Patient.get_profile_image_url", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'NoneType'>", "error": null, "timestamp": "2025-08-02T08:30:52.631724"}, {"function": "Patient.has_profile_image", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'bool'>", "error": null, "timestamp": "2025-08-02T08:30:52.634660"}, {"function": "Patient.get_patient_type_display", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.640822"}, {"function": "MedicalHistory.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.642891"}, {"function": "Vitals.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.665818"}, {"function": "Vitals.calculate_bmi", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.667174"}, {"function": "Vitals.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.668357"}, {"function": "PatientWallet.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.669255"}, {"function": "PatientWallet.credit", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.670381"}, {"function": "PatientWallet.debit", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.671386"}, {"function": "PatientWallet.get_transaction_history", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.672248"}, {"function": "PatientWallet.get_total_credits", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.673585"}, {"function": "PatientWallet.get_total_debits", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.675319"}, {"function": "PatientWallet.transfer_to", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.676225"}, {"function": "PatientWallet.get_transfer_history", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.682407"}, {"function": "PatientWallet.get_total_transfers_in", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.684120"}, {"function": "PatientWallet.get_total_transfers_out", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.685196"}, {"function": "WalletTransaction.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.686145"}, {"function": "WalletTransaction._generate_reference_number", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.687015"}, {"function": "WalletTransaction.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.688614"}, {"function": "NHIAPatientManager.get_queryset", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.689951"}, {"function": "NHIAPatient.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.690946"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.691620"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.692587"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.693699"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.698430"}, {"function": "Specialization.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.699705"}, {"function": "Doctor.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.700591"}, {"function": "Doctor.get_full_name", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.701386"}, {"function": "Doctor.get_experience_display_value", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.702004"}, {"function": "DoctorAvailability.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.702639"}, {"function": "Doctor<PERSON><PERSON>ve.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.703227"}, {"function": "DoctorEducation.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.703901"}, {"function": "DoctorExperience.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.704833"}, {"function": "Doctor<PERSON><PERSON><PERSON><PERSON>.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.705949"}, {"function": "Appointment.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.707027"}, {"function": "Appointment.is_past_due", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.708077"}, {"function": "Appointment.is_upcoming", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.709455"}, {"function": "Appointment.is_today", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.710937"}, {"function": "AppointmentFollowUp.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.718924"}, {"function": "DoctorSchedule.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.719888"}, {"function": "Doctor<PERSON><PERSON>ve.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.720604"}, {"function": "MedicationCategory.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.721672"}, {"function": "Medication.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.723868"}, {"function": "Supplier.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.724665"}, {"function": "Purchase.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.725378"}, {"function": "PurchaseApproval.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.725978"}, {"function": "PurchaseItem.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.726691"}, {"function": "PurchaseItem.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.731496"}, {"function": "Prescription.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.732220"}, {"function": "Prescription.get_total_prescribed_price", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.733142"}, {"function": "Prescription.get_patient_payable_amount", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.734810"}, {"function": "Prescription.get_pricing_breakdown", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.735596"}, {"function": "Prescription.is_payment_verified", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.736297"}, {"function": "Prescription.can_be_dispensed", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.737075"}, {"function": "Prescription.get_payment_status_display_info", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.738989"}, {"function": "PrescriptionItem.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.740374"}, {"function": "PrescriptionItem.get_medication_price", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.741283"}, {"function": "PrescriptionItem.remaining_quantity_to_dispense", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.742474"}, {"function": "PrescriptionItem.remaining_quantity", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.743626"}, {"function": "DispensingLog.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.746585"}, {"function": "DispensingLog.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.749802"}, {"function": "Dispensary.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.751385"}, {"function": "MedicationInventory.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.752024"}, {"function": "MedicationInventory.is_low_stock", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.752640"}, {"function": "MedicationInventory.is_expired", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.753480"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.754186"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.754846"}, {"function": "TestCategory.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.756081"}, {"function": "Test.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.756884"}, {"function": "TestParameter.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.757765"}, {"function": "TestRequest.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.758702"}, {"function": "TestRequest.get_total_price", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.759728"}, {"function": "TestResult.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.766299"}, {"function": "TestResultParameter.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.767032"}, {"function": "ServiceCategory.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.768686"}, {"function": "Service.__str__", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'str'>", "error": null, "timestamp": "2025-08-02T08:30:52.769514"}, {"function": "Service.get_price_with_tax", "test_type": "MODEL_METHOD", "status": "PASS", "message": "Method executed successfully: <class 'decimal.Decimal'>", "error": null, "timestamp": "2025-08-02T08:30:52.770190"}, {"function": "Invoice.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.770886"}, {"function": "Invoice.get_balance", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.771478"}, {"function": "Invoice.is_paid", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.772060"}, {"function": "Invoice.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.772676"}, {"function": "Invoice._generate_invoice_number", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.773409"}, {"function": "InvoiceItem.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.774299"}, {"function": "InvoiceItem.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.775255"}, {"function": "Payment.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.775901"}, {"function": "Payment.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.776661"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.777228"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.782780"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.784304"}, {"function": "Ward.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.785032"}, {"function": "Ward.get_available_beds_count", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.785714"}, {"function": "Ward.get_occupied_beds_count", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.786541"}, {"function": "Bed.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.787516"}, {"function": "Admission.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.788878"}, {"function": "Admission.get_duration", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.789573"}, {"function": "Admission.get_total_cost", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.790190"}, {"function": "Admission.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.790834"}, {"function": "DailyRound.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.791470"}, {"function": "NursingNote.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.792203"}, {"function": "BedTransfer.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.792869"}, {"function": "WardTransfer.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.793573"}, {"function": "ClinicalRecord.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.799860"}, {"function": "InpatientMedication.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.800733"}, {"function": "InpatientMedication.get_total_cost", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.801461"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.802228"}, {"function": "Designation.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.802963"}, {"function": "Shift.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.803635"}, {"function": "StaffSchedule.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.805070"}, {"function": "Leave.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.806038"}, {"function": "Leave.get_duration", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.806742"}, {"function": "Leave.get_leaves_by_staff", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.807521"}, {"function": "Attendance.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.808465"}, {"function": "Attendance.get_working_hours", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.809972"}, {"function": "Payroll.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.810967"}, {"function": "Payroll.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.814992"}, {"function": "StaffProfile.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.816498"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.817852"}, {"function": "ConsultingRoom.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.820624"}, {"function": "WaitingList.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.822041"}, {"function": "Consultation.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.823763"}, {"function": "ConsultationNote.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.827328"}, {"function": "Referral.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.837001"}, {"function": "SOAPNote.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.841133"}, {"function": "RadiologyCategory.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.842706"}, {"function": "RadiologyTest.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.844064"}, {"function": "RadiologyOrder.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.853578"}, {"function": "RadiologyResult.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.855026"}, {"function": "OperationTheatre.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.869196"}, {"function": "SurgeryType.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.870529"}, {"function": "Surgery.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.871887"}, {"function": "SurgicalTeam.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.906923"}, {"function": "SurgicalEquipment.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.909167"}, {"function": "EquipmentUsage.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.917077"}, {"function": "SurgerySchedule.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.918831"}, {"function": "PostOperativeNote.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.920152"}, {"function": "PreOperativeChecklist.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.921180"}, {"function": "SurgeryLog.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.922315"}, {"function": "NHIAPatient.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.923502"}, {"function": "RetainershipPatient.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.924392"}, {"function": "Report.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.925554"}, {"function": "ReportExecution.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.931824"}, {"function": "Dashboard.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.934120"}, {"function": "DashboardWidget.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.935018"}, {"function": "Invoice.save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.935827"}, {"function": "Invoice.get_balance", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.936529"}, {"function": "Invoice.is_paid", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.937096"}, {"function": "Payment.__str__", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.937659"}, {"function": "InvoiceItem.total_price", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "No test instance available", "error": null, "timestamp": "2025-08-02T08:30:52.938215"}, {"function": "save", "test_type": "MODEL_METHOD", "status": "SKIP", "message": "Skipped dangerous method", "error": null, "timestamp": "2025-08-02T08:30:52.938842"}, {"function": "is_admin", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.439245"}, {"function": "custom_login_view", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.440094"}, {"function": "dashboard_view", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.440698"}, {"function": "profile", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.441292"}, {"function": "edit_profile", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.442225"}, {"function": "staff_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.442934"}, {"function": "send_staff_onboarding_task_to_mcp", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.443552"}, {"function": "add_staff", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.445874"}, {"function": "edit_staff", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.448298"}, {"function": "delete_staff", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.450395"}, {"function": "department_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.451387"}, {"function": "add_department", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.452465"}, {"function": "edit_department", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.453039"}, {"function": "delete_department", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.453971"}, {"function": "api_users", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.454521"}, {"function": "register", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.455261"}, {"function": "create_role", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.456100"}, {"function": "edit_role", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.457022"}, {"function": "role_demo", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.458008"}, {"function": "audit_logs", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.458812"}, {"function": "permission_management", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.459407"}, {"function": "bulk_user_actions", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.459936"}, {"function": "user_privileges", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.460485"}, {"function": "delete_role", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.465323"}, {"function": "role_management", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.465920"}, {"function": "user_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.466597"}, {"function": "is_admin_or_staff", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.467300"}, {"function": "user_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.467917"}, {"function": "role_management", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.470090"}, {"function": "create_role", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.470994"}, {"function": "edit_role", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.472213"}, {"function": "delete_role", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.472976"}, {"function": "user_privileges", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.473747"}, {"function": "bulk_user_actions", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.474492"}, {"function": "permission_management", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.475217"}, {"function": "audit_logs", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.476286"}, {"function": "role_demo", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.476978"}, {"function": "UserViewSet.get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.484397"}, {"function": "UserViewSet.create", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.485437"}, {"function": "UserViewSet.update", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.486084"}, {"function": "UserViewSet.deactivate", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.486636"}, {"function": "UserViewSet.assign_roles", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.487374"}, {"function": "UserViewSet._capture_changes", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.488098"}, {"function": "UserViewSet.get_client_ip", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.488830"}, {"function": "AuditLogViewSet.get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.489384"}, {"function": "LoginView.post", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.489907"}, {"function": "get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.490442"}, {"function": "create", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.490956"}, {"function": "update", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.491472"}, {"function": "deactivate", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.492629"}, {"function": "assign_roles", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.493197"}, {"function": "_capture_changes", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.493765"}, {"function": "get_client_ip", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.498297"}, {"function": "get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.499099"}, {"function": "post", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.499798"}, {"function": "patient_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.522593"}, {"function": "register_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.523417"}, {"function": "patient_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.524015"}, {"function": "register_nhia_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.524602"}, {"function": "edit_nhia_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.525132"}, {"function": "edit_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.525689"}, {"function": "toggle_patient_status", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.526274"}, {"function": "add_funds_to_wallet", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.526797"}, {"function": "wallet_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.531636"}, {"function": "wallet_transactions", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.532990"}, {"function": "wallet_withdrawal", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.534147"}, {"function": "wallet_transfer", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.535381"}, {"function": "wallet_refund", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.538411"}, {"function": "wallet_adjustment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.547920"}, {"function": "search_patients", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.550096"}, {"function": "edit_medical_history", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.552420"}, {"function": "delete_medical_history", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.554841"}, {"function": "patient_medical_history", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.581531"}, {"function": "patient_vitals", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.585011"}, {"function": "generate_receipt", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.599165"}, {"function": "generate_medical_certificate", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.600438"}, {"function": "generate_fit_note", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.601947"}, {"function": "generate_discharge_summary", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.605719"}, {"function": "scan_barcode_qr", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.608099"}, {"function": "lookup_national_health_id", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.609016"}, {"function": "third_party_api_integration", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.614358"}, {"function": "pwa_manifest", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.616035"}, {"function": "service_worker", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.617010"}, {"function": "offline_fallback", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.618026"}, {"function": "pwa_push_demo", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.618926"}, {"function": "pwa_demo", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.620338"}, {"function": "demo_push_notification", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.622004"}, {"function": "pwa_offline_queue_demo", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.623644"}, {"function": "doctor_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.634380"}, {"function": "doctor_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.635736"}, {"function": "submit_review", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.636913"}, {"function": "manage_doctors", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.638041"}, {"function": "add_doctor", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.639085"}, {"function": "edit_doctor", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.640430"}, {"function": "delete_doctor", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.641518"}, {"function": "doctor_profile", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.642540"}, {"function": "manage_availability", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.643873"}, {"function": "delete_availability", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.648607"}, {"function": "manage_education", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.649539"}, {"function": "delete_education", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.650395"}, {"function": "manage_experience", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.651221"}, {"function": "delete_experience", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.651986"}, {"function": "request_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.652723"}, {"function": "cancel_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.655719"}, {"function": "manage_leave_requests", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.656644"}, {"function": "approve_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.657780"}, {"function": "reject_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.664509"}, {"function": "manage_specializations", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.666444"}, {"function": "edit_specialization", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.667294"}, {"function": "delete_specialization", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.668046"}, {"function": "get_doctor_availability", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.668834"}, {"function": "appointment_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.682334"}, {"function": "create_appointment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.684294"}, {"function": "appointment_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.684851"}, {"function": "edit_appointment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.685526"}, {"function": "cancel_appointment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.686161"}, {"function": "appointment_calendar", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.686693"}, {"function": "doctor_appointments", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.687245"}, {"function": "manage_doctor_schedule", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.687790"}, {"function": "delete_doctor_schedule", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.688367"}, {"function": "manage_doctor_leaves", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.688840"}, {"function": "approve_doctor_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.689850"}, {"function": "delete_doctor_leave", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.690735"}, {"function": "get_available_slots", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.691318"}, {"function": "update_appointment_status", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.691850"}, {"function": "create_prescription", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.709034"}, {"function": "dispensed_items_tracker", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.709743"}, {"function": "dispensed_item_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.710476"}, {"function": "dispensed_items_export", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.715198"}, {"function": "medication_autocomplete", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.717421"}, {"function": "pharmacy_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.718018"}, {"function": "add_dispensary", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.718578"}, {"function": "dispensary_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.719140"}, {"function": "edit_dispensary", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.719737"}, {"function": "delete_dispensary", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.720224"}, {"function": "add_dispensary_inventory_item", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.720755"}, {"function": "edit_dispensary_inventory_item", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.721509"}, {"function": "delete_dispensary_inventory_item", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.722175"}, {"function": "dispensary_inventory", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.722986"}, {"function": "inventory_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.723649"}, {"function": "add_medication", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.724457"}, {"function": "medication_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.725028"}, {"function": "edit_medication", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.725567"}, {"function": "delete_medication", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.726324"}, {"function": "manage_categories", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.726923"}, {"function": "edit_category", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.730570"}, {"function": "delete_category", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.731647"}, {"function": "manage_suppliers", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.732571"}, {"function": "edit_supplier", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.733276"}, {"function": "delete_supplier", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.734195"}, {"function": "manage_purchases", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.734874"}, {"function": "add_purchase", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.735469"}, {"function": "purchase_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.736249"}, {"function": "delete_purchase_item", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.736980"}, {"function": "submit_purchase_for_approval", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.737607"}, {"function": "approve_purchase", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.738161"}, {"function": "reject_purchase", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.738698"}, {"function": "prescription_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.739280"}, {"function": "prescription_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.739801"}, {"function": "print_prescription", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.740338"}, {"function": "update_prescription_status", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.740910"}, {"function": "dispense_prescription", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.741743"}, {"function": "dispense_prescription_original", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.742533"}, {"function": "_handle_dispensing_submission", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.743198"}, {"function": "_update_prescription_status_after_dispensing", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.743783"}, {"function": "_handle_formset_dispensing_submission", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.750002"}, {"function": "prescription_dispensing_history", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.750954"}, {"function": "add_prescription_item", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.751918"}, {"function": "add_medication_stock", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.752688"}, {"function": "quick_add_stock", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.754060"}, {"function": "delete_prescription_item", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.755552"}, {"function": "medication_api", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.756239"}, {"function": "get_stock_quantities", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.756930"}, {"function": "expiring_medications_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.757610"}, {"function": "low_stock_medications_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.758588"}, {"function": "dispensing_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.759282"}, {"function": "pharmacy_sales_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.759961"}, {"function": "debug_dispense_prescription", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.765445"}, {"function": "dispensary_inventory", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.766211"}, {"function": "prescription_payment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.766865"}, {"function": "create_prescription_invoice", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.767608"}, {"function": "laboratory_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.776712"}, {"function": "laboratory_payment_history", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.781407"}, {"function": "confirm_lab_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.783112"}, {"function": "result_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.784016"}, {"function": "result_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.784979"}, {"function": "edit_test_result", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.785697"}, {"function": "test_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.786912"}, {"function": "add_test", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.787831"}, {"function": "edit_test", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.788573"}, {"function": "delete_test", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.789322"}, {"function": "delete_parameter", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.790107"}, {"function": "manage_categories", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.790812"}, {"function": "edit_category", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.791897"}, {"function": "delete_category", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.792712"}, {"function": "lab_statistics_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.793446"}, {"function": "test_request_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.799079"}, {"function": "create_test_request", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.801535"}, {"function": "test_request_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.804858"}, {"function": "update_test_request_status", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.806920"}, {"function": "create_test_result", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.809974"}, {"function": "result_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.819054"}, {"function": "result_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.821703"}, {"function": "edit_test_result", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.823479"}, {"function": "print_result", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.825582"}, {"function": "verify_test_result", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.840988"}, {"function": "patient_tests", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.842652"}, {"function": "test_api", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.847961"}, {"function": "laboratory_sales_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.848751"}, {"function": "radiology_sales_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.849606"}, {"function": "invoice_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.859717"}, {"function": "create_invoice", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.864727"}, {"function": "invoice_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.867407"}, {"function": "edit_invoice", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.868245"}, {"function": "delete_invoice", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.869067"}, {"function": "print_invoice", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.869960"}, {"function": "record_payment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.870777"}, {"function": "service_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.871617"}, {"function": "add_service", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.873234"}, {"function": "edit_service", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.874185"}, {"function": "delete_service", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.875419"}, {"function": "patient_invoices", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.876226"}, {"function": "admission_invoices", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.880533"}, {"function": "billing_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.881707"}, {"function": "export_billing_report_csv", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.883050"}, {"function": "create_invoice_for_prescription", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.883871"}, {"function": "medication_billing_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.884938"}, {"function": "prescription_billing_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.886147"}, {"function": "process_medication_payment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.887569"}, {"function": "create_invoice_for_admission", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.888317"}, {"function": "admission_payment", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.889117"}, {"function": "inpatient_medication_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.909138"}, {"function": "inpatient_medication_list", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:54.910096"}, {"function": "bed_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.914671"}, {"function": "patient_admissions", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.916419"}, {"function": "ward_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.918561"}, {"function": "add_ward", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.922190"}, {"function": "ward_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.923077"}, {"function": "edit_ward", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.923714"}, {"function": "delete_ward", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.924488"}, {"function": "bed_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.925770"}, {"function": "add_bed", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.930768"}, {"function": "edit_bed", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.933008"}, {"function": "delete_bed", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.933828"}, {"function": "admission_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.934653"}, {"function": "admission_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.935995"}, {"function": "create_admission", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.937677"}, {"function": "edit_admission", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.938834"}, {"function": "transfer_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.939771"}, {"function": "discharge_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.941109"}, {"function": "add_clinical_record", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.942399"}, {"function": "bed_occupancy_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.943615"}, {"function": "load_beds", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.948620"}, {"function": "user_management", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.966875"}, {"function": "department_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.968148"}, {"function": "add_department", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.969647"}, {"function": "edit_department", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.970497"}, {"function": "delete_department", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.971396"}, {"function": "schedule_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.972249"}, {"function": "create_schedule", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.974064"}, {"function": "edit_schedule", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.974913"}, {"function": "delete_schedule", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.976310"}, {"function": "leave_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.980740"}, {"function": "request_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.981848"}, {"function": "approve_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.982678"}, {"function": "reject_leave", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.983665"}, {"function": "hr_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.984959"}, {"function": "attendance_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.987462"}, {"function": "add_attendance", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.988399"}, {"function": "edit_attendance", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:54.989523"}, {"function": "delete_attendance", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:54.993210"}, {"function": "payroll_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.000225"}, {"function": "add_payroll", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.002739"}, {"function": "edit_payroll", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.004367"}, {"function": "delete_payroll", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.005230"}, {"function": "consultation_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.021910"}, {"function": "consultation_payment_history", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.025182"}, {"function": "get_wallet_balance", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.033041"}, {"function": "doctor_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.034091"}, {"function": "patient_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.035081"}, {"function": "patient_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.036082"}, {"function": "create_consultation", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.038187"}, {"function": "consultation_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.039160"}, {"function": "edit_consultation", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.040028"}, {"function": "consultation_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.040989"}, {"function": "referral_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.041951"}, {"function": "create_referral", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.049992"}, {"function": "update_referral_status", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.052245"}, {"function": "consulting_room_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.053655"}, {"function": "create_consulting_room", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.056072"}, {"function": "edit_consulting_room", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.059731"}, {"function": "delete_consulting_room", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.065598"}, {"function": "waiting_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.066971"}, {"function": "add_to_waiting_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.067864"}, {"function": "update_waiting_status", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.068759"}, {"function": "doctor_waiting_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.069634"}, {"function": "start_consultation", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.071589"}, {"function": "doctor_consultation", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.072645"}, {"function": "create_prescription", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.073711"}, {"function": "create_lab_request", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.074629"}, {"function": "create_radiology_order", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.081041"}, {"function": "create_referral_from_consultation", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.082040"}, {"function": "add_soap_note", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.083077"}, {"function": "radiology_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.089847"}, {"function": "radiology_payment_history", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.099082"}, {"function": "confirm_radiology_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.099909"}, {"function": "index", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.100722"}, {"function": "order_radiology", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.101529"}, {"function": "order_detail", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.102367"}, {"function": "edit_order", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.103231"}, {"function": "schedule_order", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.105412"}, {"function": "mark_completed", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.106366"}, {"function": "cancel_order", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.107750"}, {"function": "add_result", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.108633"}, {"function": "radiology_sales_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.110117"}, {"function": "patient_radiology_results", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.115000"}, {"function": "radiology_statistics_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.117702"}, {"function": "theatre_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.139598"}, {"function": "theatre_payment_history", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.140910"}, {"function": "confirm_theatre_payment", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.141893"}, {"function": "OperationTheatreDetailView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.143235"}, {"function": "SurgeryListView.get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.149248"}, {"function": "SurgeryListView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.150129"}, {"function": "SurgeryDetailView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.151186"}, {"function": "SurgeryDetailView.post", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.152259"}, {"function": "PreOperativeChecklistCreateView.form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.153180"}, {"function": "PreOperativeChecklistCreateView.get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.155536"}, {"function": "SurgeryLogListView.get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.164359"}, {"function": "SurgeryLogListView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.165510"}, {"function": "SurgeryCreateView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.166583"}, {"function": "SurgeryCreateView.post", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.168253"}, {"function": "SurgeryCreateView.form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.169140"}, {"function": "SurgeryCreateView.form_invalid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.170751"}, {"function": "SurgicalTeamListView.get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.172867"}, {"function": "SurgicalTeamListView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.173939"}, {"function": "SurgicalTeamCreateView.form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.181731"}, {"function": "SurgicalTeamUpdateView.form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.182884"}, {"function": "SurgicalTeamDeleteView.delete", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.184741"}, {"function": "TheatreDashboardView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.185749"}, {"function": "EquipmentMaintenanceView.get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.186753"}, {"function": "SurgeryReportView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.188318"}, {"function": "theatre_statistics_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.191218"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.192626"}, {"function": "get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.200620"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.201543"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.202446"}, {"function": "post", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.203062"}, {"function": "form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.203623"}, {"function": "get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.204137"}, {"function": "get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.204700"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.205349"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.206279"}, {"function": "post", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.206910"}, {"function": "form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.208161"}, {"function": "form_invalid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.208850"}, {"function": "get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.209406"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.209940"}, {"function": "form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.210504"}, {"function": "form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.216625"}, {"function": "delete", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.218292"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.240550"}, {"function": "get_queryset", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.249832"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.250720"}, {"function": "PostOperativeNoteCreateView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.251508"}, {"function": "PostOperativeNoteCreateView.form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.252421"}, {"function": "PostOperativeNoteCreateView.get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.253400"}, {"function": "PostOperativeNoteUpdateView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.255560"}, {"function": "PostOperativeNoteUpdateView.form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.257365"}, {"function": "PostOperativeNoteUpdateView.get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.265341"}, {"function": "PostOperativeNoteDeleteView.get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.266269"}, {"function": "PostOperativeNoteDeleteView.get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.267368"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.269415"}, {"function": "form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.271300"}, {"function": "get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.272176"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.273108"}, {"function": "form_valid", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.273915"}, {"function": "get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.274822"}, {"function": "get_context_data", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.275891"}, {"function": "get_success_url", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.282523"}, {"function": "nhia_patient_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.284892"}, {"function": "register_patient_for_nhia", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.285726"}, {"function": "register_independent_nhia_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.286639"}, {"function": "retainership_patient_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.291862"}, {"function": "select_patient_for_retainership", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.297135"}, {"function": "register_patient_for_retainership", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.299327"}, {"function": "register_independent_retainership_patient", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.300182"}, {"function": "execute_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.313451"}, {"function": "generate_chart", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.314563"}, {"function": "dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.315319"}, {"function": "patient_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.316074"}, {"function": "appointment_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.316798"}, {"function": "billing_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.317468"}, {"function": "pharmacy_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.318618"}, {"function": "pharmacy_sales_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.319837"}, {"function": "laboratory_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.320851"}, {"function": "radiology_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.321478"}, {"function": "inpatient_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.322186"}, {"function": "staff_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.322877"}, {"function": "hr_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.324294"}, {"function": "financial_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.332867"}, {"function": "export_csv", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.334478"}, {"function": "export_pdf", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.335151"}, {"function": "report_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.335862"}, {"function": "create_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.336952"}, {"function": "view_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.337634"}, {"function": "edit_report", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.339147"}, {"function": "delete_report", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.339843"}, {"function": "dashboard_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.340561"}, {"function": "create_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.341271"}, {"function": "edit_dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.341913"}, {"function": "delete_dashboard", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.342555"}, {"function": "add_widget", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.343490"}, {"function": "edit_widget", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.349074"}, {"function": "delete_widget", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Skipped dangerous view", "error": null, "timestamp": "2025-08-02T08:30:55.350288"}, {"function": "export_patient_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.351483"}, {"function": "export_appointment_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.352348"}, {"function": "export_billing_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.353276"}, {"function": "export_pharmacy_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.353981"}, {"function": "export_laboratory_reports", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.355206"}, {"function": "comprehensive_transaction_history", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.357257"}, {"function": "patient_financial_summary", "test_type": "VIEW_FUNCTION", "status": "SKIP", "message": "Function not found in module", "error": null, "timestamp": "2025-08-02T08:30:55.360390"}, {"function": "home_view", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.366019"}, {"function": "notifications_list", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.366720"}, {"function": "mark_notification_read", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.367461"}, {"function": "dashboard", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.369674"}, {"function": "system_overview", "test_type": "VIEW_FUNCTION", "status": "PASS", "message": "Function imported successfully", "error": null, "timestamp": "2025-08-02T08:30:55.370533"}, {"function": "CustomLoginForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.372312"}, {"function": "CustomUserCreationForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.373047"}, {"function": "UserRegistrationForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.373925"}, {"function": "UserProfileForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.375032"}, {"function": "UserProfileForm.clean_username", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.376400"}, {"function": "UserProfileForm.clean_email", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.382575"}, {"function": "UserProfileForm.clean_contact_phone_number", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.383361"}, {"function": "UserProfileForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.384056"}, {"function": "UserProfileForm.clean_username", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.384943"}, {"function": "UserProfileForm.clean_profile_phone_number", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.385745"}, {"function": "UserProfileForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.386629"}, {"function": "StaffCreationForm.clean_roles", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.387686"}, {"function": "StaffCreationForm.clean_phone_number", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.389920"}, {"function": "StaffCreationForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.391470"}, {"function": "PhoneNumberPasswordResetForm.clean_email", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.393459"}, {"function": "PhoneNumberPasswordResetForm.get_users", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.401068"}, {"function": "PhoneNumberPasswordResetForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.402210"}, {"function": "RoleForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.404210"}, {"function": "RoleForm.clean_parent", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.405290"}, {"function": "RoleForm.clean_name", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.406368"}, {"function": "UserRoleAssignmentForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.407286"}, {"function": "BulkUserActionForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.408784"}, {"function": "PermissionFilterForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.414923"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.415989"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.417378"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.417976"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.418546"}, {"function": "clean_username", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.419684"}, {"function": "clean_email", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.420710"}, {"function": "clean_contact_phone_number", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.421778"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.422361"}, {"function": "clean_username", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.422921"}, {"function": "clean_profile_phone_number", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.423686"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.424504"}, {"function": "clean_roles", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.431882"}, {"function": "clean_phone_number", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.432568"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.433165"}, {"function": "clean_email", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.433724"}, {"function": "get_users", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.434287"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.434839"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.435440"}, {"function": "clean_parent", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.436283"}, {"function": "clean_name", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.437534"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.438148"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.438827"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.439663"}, {"function": "PatientForm.clean_special_characters", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.440556"}, {"function": "PatientForm.clean_email", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.441744"}, {"function": "PatientForm.clean_phone_number", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.442461"}, {"function": "PatientForm.clean_first_name", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.443192"}, {"function": "PatientForm.clean_last_name", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.448441"}, {"function": "PatientForm.clean_emergency_contact_name", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.449165"}, {"function": "PatientForm.clean_city", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.450446"}, {"function": "PatientForm.clean_state", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.451169"}, {"function": "PatientForm.clean_country", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.451866"}, {"function": "PatientForm.clean_occupation", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.452549"}, {"function": "PatientForm.clean_insurance_provider", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.453455"}, {"function": "PatientForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.454797"}, {"function": "MedicalHistoryForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.455499"}, {"function": "VitalsForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.456325"}, {"function": "get_specialization_choices", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.457309"}, {"function": "WalletWithdrawalForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.465445"}, {"function": "WalletWithdrawalForm.clean_amount", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.466426"}, {"function": "WalletTransferForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.467301"}, {"function": "WalletTransferForm.clean_amount", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.468124"}, {"function": "WalletTransferForm.clean_recipient_patient", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.469103"}, {"function": "WalletTransferForm.clean_description", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.472138"}, {"function": "WalletTransferForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.473556"}, {"function": "WalletAdjustmentForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.474358"}, {"function": "WalletAdjustmentForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.475078"}, {"function": "NHIARegistrationForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.484112"}, {"function": "NHIARegistrationForm.clean_nhia_reg_number", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.485790"}, {"function": "NHIAIndependentPatientForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.486733"}, {"function": "RetainershipRegistrationForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.490562"}, {"function": "RetainershipRegistrationForm.clean_retainership_reg_number", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.491566"}, {"function": "RetainershipIndependentPatientForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.492581"}, {"function": "clean_special_characters", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.493660"}, {"function": "clean_email", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.498687"}, {"function": "clean_phone_number", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.499210"}, {"function": "clean_first_name", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.499782"}, {"function": "clean_last_name", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.500520"}, {"function": "clean_emergency_contact_name", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.501034"}, {"function": "clean_city", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.501593"}, {"function": "clean_state", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.502092"}, {"function": "clean_country", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.502573"}, {"function": "clean_occupation", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.503212"}, {"function": "clean_insurance_provider", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.504161"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.504708"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.505238"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.505796"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.506277"}, {"function": "clean_amount", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.506787"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.507314"}, {"function": "clean_amount", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.508212"}, {"function": "clean_recipient_patient", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.508735"}, {"function": "clean_description", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.509859"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.514809"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.515747"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.517065"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.517631"}, {"function": "clean_nhia_reg_number", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.518259"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.518736"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.519307"}, {"function": "clean_retainership_reg_number", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.520748"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.521389"}, {"function": "NHIAIndependentPatientFormTest.test_form_saves_patient_and_nhia_patient", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.521888"}, {"function": "test_form_saves_patient_and_nhia_patient", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.522616"}, {"function": "DoctorUserCreationForm.clean_phone_number", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.523263"}, {"function": "DoctorUserCreationForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.523880"}, {"function": "get_specialization_choices", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.524416"}, {"function": "DoctorAvailabilityForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.525464"}, {"function": "DoctorLeaveForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.526312"}, {"function": "DoctorExperienceForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.527068"}, {"function": "clean_phone_number", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.532126"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.532740"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.533179"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.533640"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.534071"}, {"function": "AppointmentForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.534611"}, {"function": "AppointmentForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.535203"}, {"function": "AppointmentFollowUpForm.clean_follow_up_date", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.535736"}, {"function": "DoctorScheduleForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.536297"}, {"function": "DoctorLeaveForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.536827"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.537693"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.538574"}, {"function": "clean_follow_up_date", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.539049"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.539518"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.540117"}, {"function": "PurchaseItemForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.540795"}, {"function": "PrescriptionForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.541902"}, {"function": "PrescriptionItemForm.clean_quantity", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.542970"}, {"function": "DispenseItemForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.543705"}, {"function": "DispenseItemForm.p_item", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.552176"}, {"function": "DispenseItemForm.stock_available", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.556037"}, {"function": "DispenseItemForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.559474"}, {"function": "BaseDispenseItemFormSet.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.566005"}, {"function": "BaseDispenseItemFormSet.add_fields", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.569930"}, {"function": "BaseDispenseItemFormSet.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.575585"}, {"function": "DispensedItemsSearchForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.640582"}, {"function": "DispensaryForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.751105"}, {"function": "PrescriptionPaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.752946"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.754547"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.755296"}, {"function": "clean_quantity", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.757035"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.757745"}, {"function": "p_item", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.758902"}, {"function": "stock_available", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.760110"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.776099"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.786570"}, {"function": "add_fields", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.788234"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.788879"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.789679"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.790698"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.791551"}, {"function": "TestRequestForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.792673"}, {"function": "TestResultForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.793553"}, {"function": "TestResultForm.save", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.799380"}, {"function": "TestResultParameterForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.800089"}, {"function": "BaseTestResultParameterFormSet.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.800996"}, {"function": "BaseTestResultParameterFormSet.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.801930"}, {"function": "TestSearchForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.803171"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.804934"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.805824"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.806614"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.807288"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.808113"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.808781"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.809608"}, {"function": "LaboratoryPaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.810386"}, {"function": "LaboratoryPaymentForm.clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.816448"}, {"function": "LaboratoryPaymentForm.save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.817339"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.818641"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.819248"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.819794"}, {"function": "InvoiceForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.820505"}, {"function": "InvoiceItemForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.821072"}, {"function": "InvoiceItemForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.821578"}, {"function": "PaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.822207"}, {"function": "PaymentForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.823096"}, {"function": "AdmissionPaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.823610"}, {"function": "AdmissionPaymentForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.824175"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.824707"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.826134"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.826696"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.830981"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.831763"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.832230"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.833182"}, {"function": "get_specialization_choices", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.833653"}, {"function": "BedForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.834347"}, {"function": "AdmissionForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.834922"}, {"function": "DischargeForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.835463"}, {"function": "DailyRoundForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.836188"}, {"function": "NursingNoteForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.836718"}, {"function": "ClinicalRecordForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.837271"}, {"function": "PatientTransferForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.837817"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.838354"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.838791"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.839272"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.839844"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.840343"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.840797"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.841797"}, {"function": "InpatientMedicationPaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.843285"}, {"function": "InpatientMedicationPaymentForm.clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.847706"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.848715"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.849950"}, {"function": "ShiftForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.850573"}, {"function": "StaffScheduleForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.851119"}, {"function": "StaffScheduleForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.851718"}, {"function": "LeaveForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.852686"}, {"function": "LeaveForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.854224"}, {"function": "LeaveApprovalForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.855095"}, {"function": "LeaveApprovalForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.855893"}, {"function": "AttendanceForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.856781"}, {"function": "AttendanceForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.857895"}, {"function": "PayrollForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.858679"}, {"function": "PayrollForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.859919"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.864929"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.865728"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.866484"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.867017"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.867589"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.868231"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.869056"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.869582"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.870724"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.871336"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.871943"}, {"function": "get_active_consulting_rooms", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.872447"}, {"function": "ConsultationForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.873376"}, {"function": "ReferralForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.874189"}, {"function": "VitalsSelectionForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.874759"}, {"function": "WaitingListForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.875590"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.876342"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.880816"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.882009"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.883493"}, {"function": "ConsultationPaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.884601"}, {"function": "ConsultationPaymentForm.clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.885186"}, {"function": "ConsultationPaymentForm.save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.885690"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.886281"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.887306"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.887853"}, {"function": "RadiologyOrderForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.888397"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.888956"}, {"function": "RadiologyPaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.889515"}, {"function": "RadiologyPaymentForm.clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.890225"}, {"function": "RadiologyPaymentForm.save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.890745"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.891433"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.892129"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.893233"}, {"function": "SurgeryForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.897711"}, {"function": "SurgeryForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.898399"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.898916"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.899441"}, {"function": "TheatrePaymentForm.__init__", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.899934"}, {"function": "TheatrePaymentForm.clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.900548"}, {"function": "TheatrePaymentForm.save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Form class not found", "error": null, "timestamp": "2025-08-02T08:30:55.901109"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.901609"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.902290"}, {"function": "save", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.903130"}, {"function": "ReportExecutionForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.903984"}, {"function": "DashboardWidgetForm.__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.904494"}, {"function": "DashboardWidgetForm.clean", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form method accessible", "error": null, "timestamp": "2025-08-02T08:30:55.905011"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.905545"}, {"function": "__init__", "test_type": "FORM_FUNCTION", "status": "PASS", "message": "Form function accessible", "error": null, "timestamp": "2025-08-02T08:30:55.906262"}, {"function": "clean", "test_type": "FORM_FUNCTION", "status": "SKIP", "message": "Function not found", "error": null, "timestamp": "2025-08-02T08:30:55.906808"}, {"function": "Patient.objects.count", "test_type": "DATABASE_OP", "status": "PASS", "message": "Count: 9", "error": null, "timestamp": "2025-08-02T08:30:55.909146"}, {"function": "Patient.objects.exists", "test_type": "DATABASE_OP", "status": "PASS", "message": "Exists: True", "error": null, "timestamp": "2025-08-02T08:30:55.910385"}, {"function": "Patient.objects.first", "test_type": "DATABASE_OP", "status": "PASS", "message": "First: <class 'patients.models.Patient'>", "error": null, "timestamp": "2025-08-02T08:30:55.918457"}, {"function": "CustomUser.objects.count", "test_type": "DATABASE_OP", "status": "PASS", "message": "Count: 20", "error": null, "timestamp": "2025-08-02T08:30:55.920162"}, {"function": "CustomUser.objects.exists", "test_type": "DATABASE_OP", "status": "PASS", "message": "Exists: True", "error": null, "timestamp": "2025-08-02T08:30:55.924335"}, {"function": "CustomUser.objects.first", "test_type": "DATABASE_OP", "status": "PASS", "message": "First: <class 'accounts.models.CustomUser'>", "error": null, "timestamp": "2025-08-02T08:30:55.926001"}, {"function": "Medication.objects.count", "test_type": "DATABASE_OP", "status": "PASS", "message": "Count: 26", "error": null, "timestamp": "2025-08-02T08:30:55.931652"}, {"function": "Medication.objects.exists", "test_type": "DATABASE_OP", "status": "PASS", "message": "Exists: True", "error": null, "timestamp": "2025-08-02T08:30:55.933012"}, {"function": "Medication.objects.first", "test_type": "DATABASE_OP", "status": "PASS", "message": "First: <class 'pharmacy.models.Medication'>", "error": null, "timestamp": "2025-08-02T08:30:55.942261"}, {"function": "Service.objects.count", "test_type": "DATABASE_OP", "status": "PASS", "message": "Count: 23", "error": null, "timestamp": "2025-08-02T08:30:55.947634"}, {"function": "Service.objects.exists", "test_type": "DATABASE_OP", "status": "PASS", "message": "Exists: True", "error": null, "timestamp": "2025-08-02T08:30:55.949478"}, {"function": "Service.objects.first", "test_type": "DATABASE_OP", "status": "PASS", "message": "First: <class 'billing.models.Service'>", "error": null, "timestamp": "2025-08-02T08:30:55.951237"}, {"function": "Department.objects.count", "test_type": "DATABASE_OP", "status": "PASS", "message": "Count: 31", "error": null, "timestamp": "2025-08-02T08:30:55.952803"}, {"function": "Department.objects.exists", "test_type": "DATABASE_OP", "status": "PASS", "message": "Exists: True", "error": null, "timestamp": "2025-08-02T08:30:55.955442"}, {"function": "Department.objects.first", "test_type": "DATABASE_OP", "status": "PASS", "message": "First: <class 'accounts.models.Department'>", "error": null, "timestamp": "2025-08-02T08:30:55.957815"}]}